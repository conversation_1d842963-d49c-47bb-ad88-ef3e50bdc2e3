import 'dart:convert';

import 'package:callitris/pages/auth_provider.dart';
import 'package:callitris/pages/clients/code_verification_page.dart';
import 'package:callitris/pages/clients/user_page_boutique.dart';
import 'package:callitris/services/webview_payment_service.dart';
import 'package:callitris/services/payment_config_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class BoutiquePaymentMethodPage extends StatefulWidget {
  final String paymentMethod;
  final String clientId;
  final Map<String, dynamic> kitData;
  final Map<String, dynamic> clientData;

  const BoutiquePaymentMethodPage({
    super.key,
    required this.paymentMethod,
    required this.clientId,
    required this.kitData,
    required this.clientData,
  });

  @override
  State<BoutiquePaymentMethodPage> createState() =>
      _BoutiquePaymentMethodPageState();
}

class _BoutiquePaymentMethodPageState extends State<BoutiquePaymentMethodPage>
    with WidgetsBindingObserver, RouteAware {
  final TextEditingController _montantController = TextEditingController();
  final TextEditingController _numeroController = TextEditingController();
  final GlobalKey<ScaffoldMessengerState> _scaffoldMessengerKey =
      GlobalKey<ScaffoldMessengerState>();

  String? transactionCode;
  double? monnaie;
  Client? client;
  Map<String, dynamic>? clientDatas;
  bool _isMontantValid = false;
  String? _montantErrorMessage;
  double montantJ = 0;
  int montantSaisi = 0;
  int nbrePaye = 0;
  int quotient = 0;
  int monnaieExact = 0;
  double resteMonnaie = 0;
  bool useMonnaie = false;
  bool isSubmitting = false;
  bool _isPageActive = true;

  void recalculerMontants(StateSetter updateState) {
    try {
      double monnaieDisponible = monnaie ?? 0;

      // Cas 1 : Si monnaie >= montant journalier, forcer l'utilisation
      if (monnaieDisponible >= montantJ && !useMonnaie) {
        useMonnaie = true;
      }

      double monnaieToUse = useMonnaie ? monnaieDisponible : 0;

      // Calculer le total avec ou sans monnaie
      double totalMontant = montantSaisi + monnaieToUse;

      if (montantJ > 0 && totalMontant >= montantJ) {
        // Calculer le nombre de jours payés
        quotient = (totalMontant / montantJ).floor();

        // La monnaie est le modulo du montant saisi par le montant journalier
        // Quelque soit le résultat, considérer comme monnaie
        resteMonnaie = (montantSaisi + monnaie!) % montantJ;

        // Calculer la monnaie exacte utilisée pour ce paiement
        if (useMonnaie) {
          double montantNecessaire = quotient * montantJ;
          if (montantSaisi >= montantNecessaire) {
            monnaieExact = 0; // Pas besoin de monnaie
          } else {
            monnaieExact = (montantNecessaire - montantSaisi).toInt();
          }
        } else {
          monnaieExact = 0;
        }
      } else {
        // Si le montant total est insuffisant, la monnaie reste le modulo
        resteMonnaie = montantSaisi > 0 ? (montantSaisi % montantJ) : 0;
        quotient = 0;
        monnaieExact = 0;
      }

      updateState(() {});
    } catch (e) {
      resteMonnaie = 0;
      quotient = 0;
      monnaieExact = 0;
      updateState(() {});
    }
  }

  void saveData(String name, String data) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(name, data);
  }

  Future<String?> readData(String name) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString(name);
    return token;
  }

  Future<void> deleteData(String name) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(name);
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeApp();
  }

  @override
  void dispose() {
    _isPageActive = false;
    WidgetsBinding.instance.removeObserver(this);
    _montantController.dispose();
    _numeroController.dispose();
    super.dispose();
  }

  // Méthodes RouteAware pour détecter le retour sur la page
  @override
  void didPopNext() {
    // Cette méthode est appelée quand on revient sur cette page depuis une autre
    super.didPopNext();
    if (mounted && _isPageActive) {
      // Recharger la page après retour (ex: depuis WebView)
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted && _isPageActive) {
          _reloadPage();
        }
      });
    }
  }

  void _showSafeSnackBar(String message,
      {Color backgroundColor = Colors.red, Duration? duration}) {
    if (!mounted) {
      print('Widget non monté, SnackBar ignoré: $message');
      return;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      try {
        if (_scaffoldMessengerKey.currentState != null) {
          _scaffoldMessengerKey.currentState!.showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor,
              duration: duration ?? const Duration(seconds: 3),
            ),
          );
          return;
        }

        // Stratégie 2: Utiliser le contexte si le widget est encore monté
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor,
              duration: duration ?? const Duration(seconds: 3),
            ),
          );
          return;
        }
      } catch (e) {
        print('Erreur lors de l\'affichage du SnackBar: $e');
        print('Message: $message');
      }
    });
  }

  AuthProvider? _getSafeAuthProvider() {
    if (mounted) {
      try {
        return Provider.of<AuthProvider>(context, listen: false);
      } catch (e) {
        print('Erreur lors de l\'accès au AuthProvider: $e');
        return null;
      }
    }
    return null;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed && _isPageActive && mounted) {
      // L'app revient au premier plan, recharger la page seulement si on est encore sur cette page
      _reloadPage();
    }
  }

  void _reloadPage() {
    _initializeApp();
  }

  Future<void> fetchMonnaie() async {
    try {
      final token = Provider.of<AuthProvider>(context, listen: false).token;
      final provide = Provider.of<AuthProvider>(context, listen: false);

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'client/getMonnaie.php?clientId=${widget.clientData['id_client']}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        String monnaieValue = responseData['montant'].toString();
        setState(() {
          monnaie = double.parse(monnaieValue);
        });
      } else {
        print(
            'Erreur lors de la récupération de la monnaie : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération de la monnaie : $error');
    }
  }

  Future<void> _initializeApp() async {
    await _initPrefs();
    await fetchClientData();
    await fetchMonnaie();

    print("Montant journalier : ${widget.kitData}");

    montantJ = double.parse(widget.kitData['cout_journal'].toString());

    _numeroController.text = widget.clientData['telephone'] ??
        widget.clientData['telephone_client'] ??
        '';
  }

  Future<void> fetchClientData() async {
    try {
      final token = Provider.of<AuthProvider>(context, listen: false).token;
      final provide = Provider.of<AuthProvider>(context, listen: false);

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'client/getClientById.php?id_client=${widget.clientId}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        clientDatas = responseData;
        String id = responseData['id_client'].toString();
        String nom = responseData['nom_client'].toString();
        String prenom = responseData['prenom_client'].toString();
        String contact = responseData['telephone_client'].toString();
        String contact2 = responseData['telephone2_client'].toString();
        String adresse = responseData['domicile_client'].toString();

        setState(() {
          client = Client(
            id: id,
            nom: nom,
            prenom: prenom,
            contact: contact,
            contact2: contact2,
            adresse: adresse,
          );
        });
      } else {
        print('Erreur : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération des données du client: $error');
    }
  }

  Future<void> _initPrefs() async {
    transactionCode = await readData('boutique_payment_code');
    try {
      if (transactionCode != null && transactionCode!.isNotEmpty) {
        if (widget.paymentMethod == 'wave_ci') {
          await checkTransactionWaveState();
        } else {
          await checkTransactionState();
        }
      }
    } catch (error) {
      print('Erreur lors de l\'initialisation des préférences: $error');
    }
  }

  void _validateMontant(String value) {
    setState(() {
      if (value.trim().isEmpty) {
        _isMontantValid = false;
        _montantErrorMessage = null;
        montantSaisi = 0;
        return;
      }

      try {
        double montantSaisiDouble = double.parse(value);
        montantSaisi = montantSaisiDouble.toInt();

        if (montantSaisi <= 0) {
          _isMontantValid = false;
          _montantErrorMessage = 'Le montant doit être supérieur à 0';
          return;
        }

        // Vérifier que le montant est un multiple de 5
        if (montantSaisi % 5 != 0) {
          _isMontantValid = false;
          _montantErrorMessage = 'Le montant doit être un multiple de 5';
          return;
        }

        if (montantJ <= 0) {
          _isMontantValid = false;
          _montantErrorMessage = 'Montant journalier invalide';
          return;
        }

        double monnaieDisponible = monnaie ?? 0;

        // Cas 1 : Si monnaie >= montant journalier, exiger l'utilisation de la monnaie
        if (monnaieDisponible >= montantJ) {
          if (!useMonnaie) {
            _isMontantValid = false;
            _montantErrorMessage =
                'Vous devez utiliser votre monnaie (${monnaieDisponible.toInt()} FCFA) car elle est supérieure ou égale au montant journalier.';
            return;
          }

          // Permettre au client de saisir n'importe quel montant tant qu'il est >= montant jour
          if (useMonnaie) {
            if (montantSaisi + monnaieDisponible < montantJ) {
              _isMontantValid = false;
              _montantErrorMessage =
                  'Le montant saisi doit être au moins égal au montant journalier de ${montantJ.toInt()} FCFA';
              return;
            }
          } else {
            if (montantSaisi < montantJ) {
              _isMontantValid = false;
              _montantErrorMessage =
                  'Le montant saisi doit être au moins égal au montant journalier de ${montantJ.toInt()} FCFA';
              return;
            }
          }
        }
        // Cas 2 : S'il n'y a pas de monnaie (ou monnaie < montant journalier)
        else {
          // Permettre au client de saisir n'importe quel montant tant qu'il est >= montant jour
          // Permettre au client de saisir n'importe quel montant tant qu'il est >= montant jour
          if (useMonnaie) {
            if (montantSaisi + monnaieDisponible < montantJ) {
              _isMontantValid = false;
              _montantErrorMessage =
                  'Le montant saisi doit être au moins égal au montant journalier de ${montantJ.toInt()} FCFA';
              return;
            }
          } else {
            if (montantSaisi < montantJ) {
              _isMontantValid = false;
              _montantErrorMessage =
                  'Le montant saisi doit être au moins égal au montant journalier de ${montantJ.toInt()} FCFA';
              return;
            }
          }
        }

        // Si l'utilisateur utilise la monnaie, vérifier la cohérence
        if (useMonnaie) {
          double totalAvecMonnaie = montantSaisi + monnaieDisponible;
          if (totalAvecMonnaie < montantJ) {
            _isMontantValid = false;
            _montantErrorMessage =
                'Le montant total ($montantSaisi + ${monnaieDisponible.toInt()} = ${totalAvecMonnaie.toInt()}) doit être au moins égal au journalier (${montantJ.toInt()})';
            return;
          }

          // Nouvelle logique : Si total + montant saisi > montant journalier,
          // forcer le client à saisir un montant + monnaie >= montant journalier
          if (totalAvecMonnaie > montantJ) {
            double montantMinimum = montantJ - monnaieDisponible;
            if (montantSaisi < montantMinimum) {
              _isMontantValid = false;
              _montantErrorMessage =
                  'Avec votre monnaie de ${monnaieDisponible.toInt()} FCFA, vous devez saisir au minimum ${montantMinimum.toInt()} FCFA pour atteindre le montant journalier.';
              return;
            }
          }
        }

        _isMontantValid = true;
        _montantErrorMessage = null;

        recalculerMontants(setState);
      } catch (e) {
        _isMontantValid = false;
        _montantErrorMessage = 'Montant invalide';
        montantSaisi = 0;
      }
    });
  }

  Widget? _getMontantValidationIcon() {
    if (_montantController.text.trim().isEmpty) {
      return null;
    }

    return Icon(
      _isMontantValid ? Icons.check_circle : Icons.error,
      color: _isMontantValid ? Colors.green : Colors.red,
      size: 20,
    );
  }

  String? _getMontantHelperText() {
    if (_montantController.text.trim().isEmpty) {
      return 'Entrez un multiple de 5 (minimum ${montantJ.toInt()} FCFA)';
    }

    if (_montantErrorMessage != null) {
      return _montantErrorMessage;
    }

    if (_isMontantValid) {
      try {
        if (useMonnaie) {
          double montantSaisi = double.parse(_montantController.text);
          int nombreJours = ((montantSaisi + monnaie!) / montantJ).floor();
          double monnaieRestante = (montantSaisi + monnaie!) % montantJ;
          return '✓ Valide - $nombreJours jour${nombreJours > 1 ? 's' : ''} + ${monnaieRestante.toInt()} FCFA de monnaie';
        } else {
          double montantSaisi = double.parse(_montantController.text);
          int nombreJours = (montantSaisi / montantJ).floor();
          double monnaieRestante = (montantSaisi + monnaie!) % montantJ;
          return '✓ Valide - $nombreJours jour${nombreJours > 1 ? 's' : ''} + ${monnaieRestante.toInt()} FCFA de monnaie';
        }
      } catch (e) {
        return null;
      }
    }

    return null;
  }

  Color _getMontantValidationColor() {
    if (_montantController.text.trim().isEmpty) {
      return Colors.grey.shade600;
    }

    return _isMontantValid ? Colors.green : Colors.red;
  }

  Future<void> checkTransactionWaveState() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;

      if (token == null) {
        print('Token manquant');
        return;
      }

      if (transactionCode == null) {
        print('Code de paiement manquant');
        return;
      }

      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/wave_pay_chek.php')),
        body: {
          'transaction_code': transactionCode!,
        },
        headers: {'Authorization': token},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        print('Wave transaction check: $responseData');

        if (responseData['payment_status'] == 'succeeded') {
          _showPaymentSuccess(responseData['payment_status']);
        } else if (responseData['payment_status'] == 'processing') {
          _showPaymentSuccess(responseData['payment_status']);
        } else {
          _showPaymentSuccess(responseData['payment_status']);
        }
      } else {
        print('Erreur Wave check : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la vérification Wave : $error');
    }
  }

  Future<void> checkTransactionState() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;

      if (token == null) {
        print('Token manquant');
        return;
      }
      if (transactionCode == null) {
        print('Code de paiement manquant');
        return;
      }

      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/CinetPayNotify.php')),
        body: {
          'cpm_trans_id': transactionCode!,
        },
        headers: {'Authorization': token},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        print('CinetPay transaction check: $responseData');

        if (responseData['data']['status'] == 'ACCEPTED') {
          _showPaymentSuccess(responseData['data']['status']);
        } else if (responseData['data']['status'] == 'PENDING') {
          _showPaymentSuccess(responseData['data']['status']);
        } else {
          _showPaymentSuccess(responseData['data']['status']);
        }
      } else {
        print('Erreur CinetPay check : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la vérification CinetPay : $error');
    }
  }

  void _showPaymentSuccess(String status) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: _buildStatusCard(status),
        );
      },
    );
  }

  Future<void> _navigateConfirm() async {
    final token = Provider.of<AuthProvider>(context, listen: false).token;
    final provide = Provider.of<AuthProvider>(context, listen: false);
    final user = Provider.of<AuthProvider>(context, listen: false).user;
    String personnelId = user!['id_personnel'].toString();
    setState(() {
      isSubmitting = true;
    });

    if (token == null) {
      _showSafeSnackBar('Session expirée. Veuillez vous reconnecter.');
      return;
    }

    try {
      // Étape 1: Créer la commande boutique
      String? commandeId = await _createBoutiqueCommande();
      if (commandeId == null) {
        _showSafeSnackBar('Erreur lors de la création de la commande');
        return;
      }

      final versementData = {
        'command_id': commandeId,
        'client_id': widget.clientId,
        'monnaieExact': monnaieExact,
        'resteMonnaie': resteMonnaie,
        'personnel_id': personnelId,
        'monnaie': monnaie,
        'transaction_id': transactionCode,
      };

      final versementResponse = await http.post(
        Uri.parse(provide.getEndpoint('products/send_verify_sms.php')),
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
        },
        body: jsonEncode(versementData),
      );

      print("Versement response: ${versementResponse.body}");
      print("Versement status code: ${versementResponse.statusCode}");
      if (versementResponse.statusCode == 200) {
        // Nettoyer les données de paiement temporaires
        await deleteData('boutique_payment_code');

        _showSafeSnackBar(
          'Commande créée et premier versement enregistré avec succès!',
          backgroundColor: Colors.green,
        );

        Navigator.of(context).pop();
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => CodeVerificationPage(
              clientId: widget.clientId,
              commandeId: commandeId,
              transactionId: transactionCode!,
              clientData: clientDatas!,
            ),
          ),
        );
      } else {
        _showSafeSnackBar('Erreur lors de l\'enregistrement du versement');
        setState(() {
          isSubmitting = false;
        });
      }
    } catch (error) {
      print("Erreur lors de la finalisation: $error");
      _showSafeSnackBar("Erreur lors de la finalisation de la commande");
      setState(() {
        isSubmitting = false;
      });
    }
  }

  Widget _buildStatusCard(String status) {
    if (status == 'ACCEPTED' || status == 'succeeded') {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(40),
            ),
            child: Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 50,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Paiement Accepté',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Votre paiement a été vérifié avec succès!',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: isSubmitting
                ? const Center(
                    child: CircularProgressIndicator(
                      color: Colors.green,
                      strokeWidth: 3.0,
                    ),
                  )
                : ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      _navigateConfirm();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      padding: const EdgeInsets.symmetric(vertical: 15),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                    child: const Text(
                      'Continuer',
                      style: TextStyle(fontSize: 16, color: Colors.white),
                    ),
                  ),
          ),
        ],
      );
    } else if (status == 'PENDING' || status == 'processing') {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.red.shade100,
              borderRadius: BorderRadius.circular(40),
            ),
            child: Icon(
              Icons.error,
              color: Colors.red,
              size: 50,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Paiement Échoué',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Votre paiement a été refusé. Veuillez réessayer.',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _clearPaymentCodeAndReload();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Réessayer',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
          ),
        ],
      );
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.red.shade100,
              borderRadius: BorderRadius.circular(40),
            ),
            child: Icon(
              Icons.error,
              color: Colors.red,
              size: 50,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Paiement Échoué',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Votre paiement a été refusé. Veuillez réessayer.',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _clearPaymentCodeAndReload();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Réessayer',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
          ),
        ],
      );
    }
  }

  Future<void> _clearPaymentCodeAndReload() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('boutique_payment_code');
    setState(() {
      transactionCode = null;
    });
    _reloadPage();
  }

  Future<String?> _createBoutiqueCommande() async {
    try {
      final provide = _getSafeAuthProvider();
      if (provide == null) return null;

      final token = provide.token;
      final user = provide.user;
      if (token == null || user == null) return null;

      String idPersonnel = user['id_personnel'].toString();

      final requestData = {
        'clientId': widget.clientId,
        'personnelId': idPersonnel,
        'id_kit': widget.kitData['id_kit'],
        'qte': 1,
      };

      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/addCom.php')),
        headers: {'Authorization': token, 'Content-Type': 'application/json'},
        body: jsonEncode(requestData),
      );
      print("response du serveur : ${response.body}");
      if (response.statusCode == 200) {
        return jsonDecode(response.body)['id_commande'].toString();
      } else {
        print(
            "Erreur création commande: ${response.statusCode} - ${response.body}");
        return null;
      }
    } catch (error) {
      print("Erreur lors de la création de la commande: $error");
      return null;
    }
  }

  String _getPaymentMethodName() {
    switch (widget.paymentMethod) {
      case 'orange_money':
        return 'Orange Money';
      case 'mtn_momo':
        return 'MTN MoMo';
      case 'moov_money':
        return 'MOOV Money';
      case 'moov_ci':
        return 'MOOV CI';
      case 'wave_ci':
        return 'WAVE CI';
      default:
        return 'Inconnu';
    }
  }

  Color _getPaymentMethodColor() {
    switch (widget.paymentMethod) {
      case 'orange_money':
        return Colors.orange;
      case 'mtn_momo':
        return Colors.yellow.shade700;
      case 'moov_money':
        return Colors.blue;
      case 'wave_ci':
        return const Color.fromARGB(255, 44, 136, 240);
      default:
        return Colors.grey;
    }
  }

  String _getPaymentMethodImage() {
    switch (widget.paymentMethod) {
      case 'orange_money':
        return 'assets/orange_money.png';
      case 'mtn_momo':
        return 'assets/mtn_momo.png';
      case 'moov_money':
        return 'assets/moov_money.png';
      case 'wave_ci':
        return 'assets/wave_ci.png';
      default:
        return 'assets/default_payment.png';
    }
  }

  Future<void> _processPayment() async {
    final provide = _getSafeAuthProvider();
    if (provide == null) {
      _showSafeSnackBar(
          'Erreur d\'authentification. Veuillez redémarrer l\'application.');
      return;
    }

    final token = provide.token;
    if (token == null) {
      _showSafeSnackBar('Session expirée. Veuillez vous reconnecter.');
      return;
    }

    // Pour la boutique, on fait le paiement avec un command_id temporaire
    // La commande sera créée après validation du paiement
    final Map<String, dynamic> requestBody = {
      'amount': _montantController.text,
      'phone': _numeroController.text,
      'command_id': '',
      'client_id': widget.clientId,
      'kit_id': widget.kitData['id_kit'],
      'is_boutique': true,
      'channels': "MOBILE_MONEY",
    };

    try {
      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/addTransaction.php')),
        body: json.encode(requestBody),
        headers: {'Authorization': token, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        if (response.body.trim().startsWith('{') ||
            response.body.trim().startsWith('[')) {
          final Map<String, dynamic> responseData = jsonDecode(response.body);
          print('Parsed response: $responseData');

          if (responseData['success'] == true) {
            final paymentUrl =
                responseData['payment_url']?.toString().replaceAll('\\', '') ??
                    '';
            saveData("boutique_payment_code", responseData['transaction_id']);

            if (paymentUrl.isNotEmpty) {
              await _launchPaymentUrl(paymentUrl);
            }
          } else {
            _showSafeSnackBar(
              responseData['message']?.toString() ?? 'Erreur de paiement',
            );
          }
        } else {
          print("Erreur serveur: ${response.body}");
          _showSafeSnackBar(
            "Erreur du serveur: ${response.body}",
            duration: const Duration(seconds: 5),
          );
        }
      } else {
        print("HTTP Error: ${response.statusCode} - Body: ${response.body}");
        _showSafeSnackBar(
            "Erreur HTTP: ${response.statusCode} - ${response.body}");
      }
    } catch (error) {
      print("Erreur lors du paiement: $error");
      _showSafeSnackBar("Erreur lors du paiement. Veuillez réessayer.");
    }
  }

  Future<void> _launchPaymentUrl(String url) async {
    try {
      // Vérifier si c'est un paiement CinetPay et si on doit utiliser la WebView
      final isCinetPay = widget.paymentMethod.contains('cinetpay') ||
          url.contains('cinetpay') ||
          url.contains('checkout.cinetpay.com');

      if (isCinetPay) {
        final useWebView =
            await PaymentConfigService.shouldUseWebViewForCinetPay();
        if (useWebView) {
          await _launchCinetPayWebView(url);
        } else {
          // Utiliser le navigateur externe même pour CinetPay si configuré ainsi
          await launchUrl(
            Uri.parse(url),
            mode: LaunchMode.externalApplication,
          );
        }
      } else {
        // Pour les autres méthodes, utiliser le navigateur externe
        await launchUrl(
          Uri.parse(url),
          mode: LaunchMode.externalApplication,
        );
      }
    } catch (e) {
      print("Erreur: $e");
      await Clipboard.setData(ClipboardData(text: url));
      _showSafeSnackBar(
        "URL copiée dans le presse-papiers",
        backgroundColor: Colors.orange,
      );
    }
  }

  Future<void> _launchCinetPayWebView(String url) async {
    try {
      // Récupérer l'ID de transaction depuis les données sauvegardées
      final transactionId =
          await readData("boutique_payment_code") ?? 'unknown';

      // Utiliser le service WebView directement et récupérer le résultat
      final bool paymentSuccess =
          await WebViewPaymentService.launchCinetPayWebView(
        context: context,
        paymentUrl: url,
        transactionId: transactionId,
        commandId: '', // Pas de commandId pour la boutique
        clientId: widget.clientId,
        autoCloseOnCompletion: true, // Option pour fermer automatiquement
        onPaymentCompleted: () {
          _showSafeSnackBar(
            'Paiement réussi !',
            backgroundColor: Colors.green,
          );
        },
        onPaymentFailed: (reason) {
          _showSafeSnackBar(
            'Paiement échoué: $reason',
            backgroundColor: Colors.red,
          );
        },
        onPaymentCancelled: () {
          _showSafeSnackBar(
            'Paiement annulé',
            backgroundColor: Colors.orange,
          );
        },
      );

      // Si le paiement a réussi, recharger l'application
      if (paymentSuccess) {
        _initializeApp();
      }
    } catch (e) {
      print("Erreur WebView: $e");
      // Fallback vers le navigateur externe
      await launchUrl(
        Uri.parse(url),
        mode: LaunchMode.externalApplication,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldMessenger(
      key: _scaffoldMessengerKey,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: Text(
            'Paiement ${_getPaymentMethodName()}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: _getPaymentMethodColor(),
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.white),
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      _getPaymentMethodColor(),
                      _getPaymentMethodColor().withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(30),
                    bottomRight: Radius.circular(30),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(20, 0, 20, 40),
                  child: Column(
                    children: [
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(50),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 3,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.15),
                              blurRadius: 15,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(47),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Image.asset(
                              _getPaymentMethodImage(),
                              fit: BoxFit.contain,
                              width: double.infinity,
                              height: double.infinity,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        _getPaymentMethodName(),
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              blurRadius: 10,
                              color: Colors.black26,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'Paiement mobile sécurisé',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildKitInfoCard(),
                    const SizedBox(height: 24),
                    _buildPaymentForm(),
                    const SizedBox(height: 30),
                    _buildPaymentButton(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildKitInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      _getPaymentMethodColor().withOpacity(0.1),
                      _getPaymentMethodColor().withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(
                  Icons.shopping_bag,
                  color: _getPaymentMethodColor(),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Informations du kit',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    Text(
                      'Détails de la commande boutique',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Divider(color: Colors.grey.shade200),
          const SizedBox(height: 16),
          _buildInfoRow('Pack', widget.kitData['option_kit'] ?? 'N/A'),
          const SizedBox(height: 12),
          _buildInfoRow('Montant Total', '${widget.kitData['montant']} FCFA'),
          const SizedBox(height: 12),
          _buildInfoRow(
              'Coût Journalier', '${widget.kitData['cout_journal']} FCFA'),
          const SizedBox(height: 12),
          _buildInfoRow(
              'Total Produits', widget.kitData['total_prod'] ?? 'N/A'),
          const SizedBox(height: 12),
          _buildInfoRow('Livret', 'N° ${widget.kitData['livret']}'),
          if (monnaie != null && monnaie! > 0) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.account_balance_wallet,
                      color: Colors.green.shade600, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    'Monnaie disponible: ${monnaie!.toInt()} FCFA',
                    style: TextStyle(
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            '$label:',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentForm() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informations de paiement',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _numeroController,
            keyboardType: TextInputType.phone,
            decoration: InputDecoration(
              labelText: 'Numéro de téléphone',
              hintText: 'Ex: 0123456789',
              prefixIcon: Icon(Icons.phone, color: _getPaymentMethodColor()),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide:
                    BorderSide(color: _getPaymentMethodColor(), width: 2),
              ),
            ),
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _montantController,
            keyboardType: TextInputType.number,
            onChanged: _validateMontant,
            decoration: InputDecoration(
              labelText: 'Montant à payer (FCFA)',
              hintText: 'Entrez le montant',
              prefixIcon: Icon(Icons.payments, color: _getPaymentMethodColor()),
              suffixIcon: _getMontantValidationIcon(),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide:
                    BorderSide(color: _getPaymentMethodColor(), width: 2),
              ),
              helperText: _getMontantHelperText(),
              helperStyle: TextStyle(
                color: _getMontantValidationColor(),
                fontSize: 12,
              ),
            ),
          ),
          if (monnaie != null && monnaie! > 0) ...[
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: (monnaie! >= montantJ)
                    ? Colors.orange.shade50
                    : Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                    color: (monnaie! >= montantJ)
                        ? Colors.orange.shade200
                        : Colors.blue.shade200),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.account_balance_wallet,
                          color: (monnaie! >= montantJ)
                              ? Colors.orange.shade600
                              : Colors.blue.shade600),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          (monnaie! >= montantJ)
                              ? 'Utilisation de la monnaie obligatoire'
                              : 'Utiliser la monnaie disponible',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: (monnaie! >= montantJ)
                                ? Colors.orange.shade800
                                : Colors.blue.shade800,
                          ),
                        ),
                      ),
                      Switch(
                        value: useMonnaie,
                        onChanged: (value) {
                          setState(() {
                            useMonnaie = value;
                            _validateMontant(_montantController.text);
                          });
                        },
                        activeColor: (monnaie! >= montantJ)
                            ? Colors.orange.shade600
                            : Colors.blue.shade600,
                      ),
                    ],
                  ),
                  // Message d'information sur les règles
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline,
                            color: Colors.grey.shade600, size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            (monnaie! >= montantJ)
                                ? 'Votre monnaie (${monnaie!.toInt()} FCFA) est ≥ au montant journalier. Son utilisation est obligatoire.'
                                : 'Vous pouvez choisir d\'utiliser votre monnaie (${monnaie!.toInt()} FCFA) ou non.',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (useMonnaie) ...[
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text('Monnaie utilisée:',
                                  style:
                                      TextStyle(color: Colors.grey.shade600)),
                              Text('$monnaieExact FCFA',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold)),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text('Jours payés:',
                                  style:
                                      TextStyle(color: Colors.grey.shade600)),
                              Text('$quotient jour${quotient > 1 ? 's' : ''}',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold)),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text('Nouvelle monnaie:',
                                  style:
                                      TextStyle(color: Colors.grey.shade600)),
                              Text('${resteMonnaie.toInt()} FCFA',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold)),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentButton() {
    bool canPay = _isMontantValid &&
        _numeroController.text.trim().isNotEmpty &&
        transactionCode == null;

    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: canPay
              ? [
                  _getPaymentMethodColor(),
                  _getPaymentMethodColor().withOpacity(0.8)
                ]
              : [Colors.grey.shade400, Colors.grey.shade500],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: canPay
            ? [
                BoxShadow(
                  color: _getPaymentMethodColor().withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 3),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: canPay
              ? () {
                  if (widget.paymentMethod == 'wave_ci') {
                    _processWavePayment();
                  } else {
                    _processPayment();
                  }
                }
              : null,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.payment,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  canPay ? 'Procéder au paiement' : 'Procéder au paiement',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _processWavePayment() async {
    final provide = _getSafeAuthProvider();
    if (provide == null) {
      _showSafeSnackBar(
          'Erreur d\'authentification. Veuillez redémarrer l\'application.');
      return;
    }

    final token = provide.token;
    if (token == null) {
      _showSafeSnackBar('Session expirée. Veuillez vous reconnecter.');
      return;
    }

    final Map<String, dynamic> requestBody = {
      'amount': _montantController.text,
      'phone': _numeroController.text,
      'command_id': 'BOUTIQUE_TEMP',
      'client_id': widget.clientId,
      'kit_id': widget
          .kitData['id_kit'],
      'is_boutique': true,
    };

    try {
      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/wave_init_pay.php')),
        body: json.encode(requestBody),
        headers: {'Authorization': token, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        String responseBody = response.body.trim();
        if (!responseBody.startsWith('{') && !responseBody.startsWith('[')) {
          _showSafeSnackBar('Erreur serveur: $responseBody');
          return;
        }

        try {
          final Map<String, dynamic> responseData = jsonDecode(responseBody);

          if (responseData.containsKey('wave_launch_url')) {
            final waveLaunchUrl =
                responseData['wave_launch_url']?.toString() ?? '';
            final paymentId = responseData['id']?.toString() ?? '';

            if (paymentId.isNotEmpty) {
              saveData("boutique_payment_code", paymentId);
            }

            if (waveLaunchUrl.isNotEmpty) {
              await _launchPaymentUrl(waveLaunchUrl);
            }
          } else {
            _showSafeSnackBar('Erreur: URL de paiement Wave non trouvée');
          }
        } catch (e) {
          print('Erreur parsing JSON: $e');
          _showSafeSnackBar('Erreur de format de réponse du serveur');
        }
      } else if (response.statusCode == 401) {
        _showSafeSnackBar(
            'Erreur d\'authentification Wave API. Vérifiez la clé API.');
      } else {
        try {
          final errorData = jsonDecode(response.body);
          _showSafeSnackBar(
              'Erreur Wave: ${errorData['message'] ?? 'Erreur inconnue'}');
        } catch (e) {
          _showSafeSnackBar('Erreur HTTP: ${response.statusCode}');
        }
      }
    } catch (error) {
      print("Erreur lors du paiement Wave: $error");
      _showSafeSnackBar("Erreur lors du paiement Wave. Veuillez réessayer.");
    }
  }
}
