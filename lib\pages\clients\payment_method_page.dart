import 'dart:convert';

import 'package:callitris/pages/auth_provider.dart';
import 'package:callitris/pages/clients/code_verification_page.dart';
import 'package:callitris/pages/clients/user_page_boutique.dart';
import 'package:callitris/services/webview_payment_service.dart';
import 'package:callitris/services/payment_config_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

class PaymentMethodPage extends StatefulWidget {
  final String paymentMethod;
  final String clientId;
  final String commandeId;

  final Map<String, dynamic> clientData;

  const PaymentMethodPage({
    super.key,
    required this.paymentMethod,
    required this.clientId,
    required this.commandeId,
    required this.clientData,
  });

  @override
  State<PaymentMethodPage> createState() => _PaymentMethodPageState();
}

class _PaymentMethodPageState extends State<PaymentMethodPage>
    with WidgetsBindingObserver {
  final TextEditingController _montantController = TextEditingController();
  final TextEditingController _numeroController = TextEditingController();
  final GlobalKey<ScaffoldMessengerState> _scaffoldMessengerKey =
      GlobalKey<ScaffoldMessengerState>();
  String code = '';
  String? storageCommandeId;
  String? storageClientId;
  String? transactionCode;
  String? idCom;
  String? cle;
  String? livret;
  String? pack;
  String? nomProduit;
  String? nbre_jours;
  String? journalier;
  String? jour_paye;
  String? jour_reste;
  double? monnaie;
  double? monnaieReste;
  double? monnaieVers;
  Client? client;
  Map<String, dynamic>? clientDatas;
  bool _isMontantValid = false;
  String? _montantErrorMessage;
  double montantJ = 0;
  int montantSaisi = 0;
  int nbrePaye = 0;
  int quotient = 0;
  int monnaieExact = 0;
  double resteMonnaie = 0;
  bool useMonnaie = false;
  bool isSubmitting = false;
  bool _isPageActive = true;

  void recalculerMontants(StateSetter updateState) {
    try {
      double monnaieDisponible = monnaie ?? 0;

      // Cas 1 : Si monnaie >= montant journalier, forcer l'utilisation
      if (monnaieDisponible >= montantJ && !useMonnaie) {
        useMonnaie = true;
      }

      double monnaieToUse = useMonnaie ? monnaieDisponible : 0;

      // Calculer le total avec ou sans monnaie
      double totalMontant = montantSaisi + monnaieToUse;

      if (montantJ > 0 && totalMontant >= montantJ) {
        // Calculer le nombre de jours payés
        quotient = (totalMontant / montantJ).floor();

        // La monnaie est le modulo du montant saisi par le montant journalier
        // Quelque soit le résultat, considérer comme monnaie
        resteMonnaie = (montantSaisi + monnaie!) % montantJ;

        // Calculer la monnaie exacte utilisée pour ce paiement
        if (useMonnaie) {
          double montantNecessaire = quotient * montantJ;
          if (montantSaisi >= montantNecessaire) {
            monnaieExact = 0; // Pas besoin de monnaie
          } else {
            monnaieExact = (montantNecessaire - montantSaisi).toInt();
          }
        } else {
          monnaieExact = 0;
        }
      } else {
        // Si le montant total est insuffisant, la monnaie reste le modulo
        resteMonnaie = montantSaisi > 0 ? (montantSaisi % montantJ) : 0;
        quotient = 0;
        monnaieExact = 0;
      }

      updateState(() {});
    } catch (e) {
      resteMonnaie = 0;
      quotient = 0;
      monnaieExact = 0;
      updateState(() {});
    }
  }

  void saveData(String name, String data) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString(name, data);
  }

  Future<String?> readData(String name) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String? token = prefs.getString(name);
    return token;
  }

  Future<void> deleteData(String name) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove(name);
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeApp();
  }

  @override
  void dispose() {
    _isPageActive = false;
    WidgetsBinding.instance.removeObserver(this);
    _montantController.dispose();
    _numeroController.dispose();
    super.dispose();
  }

  void _showSafeSnackBar(String message,
      {Color backgroundColor = Colors.red, Duration? duration}) {
    if (!mounted) {
      print('Widget non monté, SnackBar ignoré: $message');
      return;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      try {
        if (_scaffoldMessengerKey.currentState != null) {
          _scaffoldMessengerKey.currentState!.showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor,
              duration: duration ?? const Duration(seconds: 3),
            ),
          );
          return;
        }

        // Stratégie 2: Utiliser le contexte si le widget est encore monté
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: backgroundColor,
              duration: duration ?? const Duration(seconds: 3),
            ),
          );
          return;
        }
      } catch (e) {
        print('Erreur lors de l\'affichage du SnackBar: $e');
        print('Message: $message');
      }
    });
  }

  // Méthode utilitaire pour accéder au Provider de manière sécurisée
  AuthProvider? _getSafeAuthProvider() {
    if (mounted) {
      try {
        return Provider.of<AuthProvider>(context, listen: false);
      } catch (e) {
        print('Erreur lors de l\'accès au AuthProvider: $e');
        return null;
      }
    }
    return null;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed && _isPageActive && mounted) {
      // L'app revient au premier plan, recharger la page seulement si on est encore sur cette page
      _reloadPage();
    }
  }

  void _reloadPage() {
    // Recharger les données de la page
    _initializeApp();
  }

  Future<void> fetchMonnaie() async {
    try {
      final token = Provider.of<AuthProvider>(context, listen: false).token;
      final provide = Provider.of<AuthProvider>(context, listen: false);

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'client/getMonnaie.php?clientId=${widget.clientData['id_client']}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      //print(response.body);
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        String monnaieValue = responseData['montant'].toString();
        setState(() {
          monnaie = double.parse(monnaieValue);
        });
      } else {
        print(
            'Erreur lors de la récupération de la monnaie : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération de la monnaie : $error');
    }
  }

  Future<void> _initializeApp() async {
    await _initPrefs();
    await fetchCommandeInfo();
    await fetchClientData();
    // Vérifier si une transaction d'une autre commande est en cours
    await _checkPendingTransaction();
    await fetchMonnaie();

    _numeroController.text = widget.clientData['telephone'] ??
        widget.clientData['telephone_client'] ??
        '';
  }

  Future<void> fetchClientData() async {
    try {
      final token = Provider.of<AuthProvider>(context, listen: false).token;
      final provide = Provider.of<AuthProvider>(context, listen: false);

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'client/getClientById.php?id_client=${storageClientId ?? widget.clientId}')),
        headers: {'Authorization': token!, 'Content-Type': 'application/json'},
      );
      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        clientDatas = responseData;
        String id = responseData['id_client'].toString();
        String nom = responseData['nom_client'].toString();
        String prenom = responseData['prenom_client'].toString();
        String contact = responseData['telephone_client'].toString();
        String contact2 = responseData['telephone2_client'].toString();
        String adresse = responseData['domicile_client'].toString();

        setState(() {
          client = Client(
            id: id,
            nom: nom,
            prenom: prenom,
            contact: contact,
            contact2: contact2,
            adresse: adresse,
          );
        });
      } else {
        print('Erreur : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la récupération des données du client: $error');
    }
  }

  Future<void> _initPrefs() async {
    transactionCode = await readData('payment_code');
    storageCommandeId = await readData('commande_id');
    storageClientId = await readData('client_id');
    try {
      if (transactionCode != null && transactionCode!.isNotEmpty) {
        if (widget.paymentMethod == 'wave_ci') {
          await checkTransactionWaveState();
        } else {
          await checkTransactionState();
        }
      }
    } catch (error) {
      print('Erreur lors de l\'initialisation des préférences: $error');
    }
  }

  Future<void> _checkPendingTransaction() async {
    // Vérifier si storageCommandeId existe et est différent de widget.commandeId
    if (storageCommandeId != null &&
        storageCommandeId!.isNotEmpty &&
        storageCommandeId != widget.commandeId) {
      // Récupérer les informations de la commande en cours
      Map<String, dynamic>? pendingCommandeInfo =
          await _fetchPendingCommandeInfo(storageCommandeId!);

      if (pendingCommandeInfo != null && mounted) {
        _showPendingTransactionDialog(pendingCommandeInfo);
      }
    }
  }

  Future<Map<String, dynamic>?> _fetchPendingCommandeInfo(
      String commandeId) async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;

      if (token == null) {
        print('Token manquant pour _fetchPendingCommandeInfo');
        return null;
      }
      print(widget.commandeId);
      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getCommandeById.php?commandeId=${widget.commandeId}')),
        headers: {'Authorization': token, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        // Vérifier si la réponse n'est pas vide
        if (response.body.trim().isEmpty) {
          print(
              'Erreur: Réponse vide du serveur pour _fetchPendingCommandeInfo');
          return null;
        }

        try {
          String trimmedBody = response.body.trim();
          if (!trimmedBody.startsWith('{') && !trimmedBody.startsWith('[')) {
            print(
                'Erreur: Réponse non-JSON du serveur _fetchPendingCommandeInfo: $trimmedBody');
            return null;
          }

          final List<dynamic> responseData = jsonDecode(response.body);

          if (responseData.isNotEmpty) {
            return responseData[0];
          }
        } catch (e) {
          print('Erreur parsing JSON _fetchPendingCommandeInfo: $e');
          print(
              'Contenu de la réponse _fetchPendingCommandeInfo: "${response.body}"');
        }
      } else {
        print('Erreur _fetchPendingCommandeInfo : ${response.statusCode}');
      }
    } catch (error) {
      print(
          'Erreur lors de la récupération des informations de la commande en cours : $error');
    }
    return null;
  }

  void _showPendingTransactionDialog(Map<String, dynamic> commandeInfo) {
    showDialog(
      context: context,
      barrierDismissible:
          false, // Empêche la fermeture en cliquant à l'extérieur
      builder: (BuildContext context) {
        return PopScope(
          canPop: false, // Empêche la fermeture avec le bouton retour
          child: AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            title: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.orange.shade100,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.warning_amber_rounded,
                    color: Colors.orange.shade600,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Transaction en cours',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Une transaction est déjà en cours pour une autre commande :',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.orange.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Informations de la commande en cours',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildPendingInfoRow('Client',
                            "${client!.nom.toString()} ${client!.prenom.toString()}"),
                        const SizedBox(height: 8),
                        _buildPendingInfoRow('Commande',
                            commandeInfo['code_cmd']?.toString() ?? 'N/A'),
                        const SizedBox(height: 8),
                        _buildPendingInfoRow(
                            'Pack', commandeInfo['pack']?.toString() ?? 'N/A'),
                        const SizedBox(height: 8),
                        _buildPendingInfoRow('Livret',
                            commandeInfo['livret']?.toString() ?? 'N/A'),
                        const SizedBox(height: 8),
                        _buildPendingInfoRow('Journalier',
                            commandeInfo['journalier']?.toString() ?? 'N/A'),
                        const SizedBox(height: 8),
                        _buildPendingInfoRow('Jours payés',
                            commandeInfo['paye']?.toString() ?? 'N/A'),
                        const SizedBox(height: 8),
                        _buildPendingInfoRow('Jours restants',
                            commandeInfo['reste']?.toString() ?? 'N/A'),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Que souhaitez-vous faire ?',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PaymentMethodPage(
                        paymentMethod: widget.paymentMethod,
                        clientId: storageClientId!,
                        commandeId: storageCommandeId!,
                        clientData: clientDatas!,
                      ),
                    ),
                  );
                },
                child: Text(
                  'Révenir à la transaction',
                  style: TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  await _clearPendingTransactionAndReload();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  'Annuler l\'ancienne',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPendingInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              fontSize: 12,
              color: Colors.orange.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _clearPendingTransactionAndReload() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('payment_code');
    await prefs.remove('commande_id');
    await prefs.remove('client_id');
    setState(() {
      transactionCode = null;
      storageCommandeId = null;
    });

    _showSafeSnackBar(
      'Transaction précédente annulée',
      backgroundColor: Colors.green,
    );

    _reloadPage();
  }

  void _validateMontant(String value) {
    setState(() {
      if (value.trim().isEmpty) {
        _isMontantValid = false;
        _montantErrorMessage = null;
        montantSaisi = 0;
        return;
      }

      try {
        double montantSaisiDouble = double.parse(value);
        montantSaisi = montantSaisiDouble.toInt();

        if (montantSaisi <= 0) {
          _isMontantValid = false;
          _montantErrorMessage = 'Le montant doit être supérieur à 0';
          return;
        }

        // Vérifier que le montant est un multiple de 5
        if (montantSaisi % 5 != 0) {
          _isMontantValid = false;
          _montantErrorMessage = 'Le montant doit être un multiple de 5';
          return;
        }

        if (montantJ <= 0) {
          _isMontantValid = false;
          _montantErrorMessage = 'Montant journalier invalide';
          return;
        }

        double monnaieDisponible = monnaie ?? 0;

        // Cas 1 : Si monnaie >= montant journalier, exiger l'utilisation de la monnaie
        if (monnaieDisponible >= montantJ) {
          if (!useMonnaie) {
            _isMontantValid = false;
            _montantErrorMessage =
                'Vous devez utiliser votre monnaie (${monnaieDisponible.toInt()} FCFA) car elle est supérieure ou égale au montant journalier.';
            return;
          }

          // Permettre au client de saisir n'importe quel montant tant qu'il est >= montant jour
          if (useMonnaie) {
            if (montantSaisi + monnaieDisponible < montantJ) {
              _isMontantValid = false;
              _montantErrorMessage =
                  'Le montant saisi doit être au moins égal au montant journalier de ${montantJ.toInt()} FCFA';
              return;
            }
          } else {
            if (montantSaisi < montantJ) {
              _isMontantValid = false;
              _montantErrorMessage =
                  'Le montant saisi doit être au moins égal au montant journalier de ${montantJ.toInt()} FCFA';
              return;
            }
          }
        }
        // Cas 2 : S'il n'y a pas de monnaie (ou monnaie < montant journalier)
        else {
          // Permettre au client de saisir n'importe quel montant tant qu'il est >= montant jour
          // Permettre au client de saisir n'importe quel montant tant qu'il est >= montant jour
          if (useMonnaie) {
            if (montantSaisi + monnaieDisponible < montantJ) {
              _isMontantValid = false;
              _montantErrorMessage =
                  'Le montant saisi doit être au moins égal au montant journalier de ${montantJ.toInt()} FCFA';
              return;
            }
          } else {
            if (montantSaisi < montantJ) {
              _isMontantValid = false;
              _montantErrorMessage =
                  'Le montant saisi doit être au moins égal au montant journalier de ${montantJ.toInt()} FCFA';
              return;
            }
          }
        }

        // Si l'utilisateur utilise la monnaie, vérifier la cohérence
        if (useMonnaie) {
          double totalAvecMonnaie = montantSaisi + monnaieDisponible;
          if (totalAvecMonnaie < montantJ) {
            _isMontantValid = false;
            _montantErrorMessage =
                'Le montant total ($montantSaisi + ${monnaieDisponible.toInt()} = ${totalAvecMonnaie.toInt()}) doit être au moins égal au journalier (${montantJ.toInt()})';
            return;
          }

          // Nouvelle logique : Si total + montant saisi > montant journalier,
          // forcer le client à saisir un montant + monnaie >= montant journalier
          if (totalAvecMonnaie > montantJ) {
            double montantMinimum = montantJ - monnaieDisponible;
            if (montantSaisi < montantMinimum) {
              _isMontantValid = false;
              _montantErrorMessage =
                  'Avec votre monnaie de ${monnaieDisponible.toInt()} FCFA, vous devez saisir au minimum ${montantMinimum.toInt()} FCFA pour atteindre le montant journalier.';
              return;
            }
          }
        }

        _isMontantValid = true;
        _montantErrorMessage = null;

        recalculerMontants(setState);
      } catch (e) {
        _isMontantValid = false;
        _montantErrorMessage = 'Montant invalide';
        montantSaisi = 0;
      }
    });
  }

  Widget? _getMontantValidationIcon() {
    if (_montantController.text.trim().isEmpty) {
      return null;
    }

    return Icon(
      _isMontantValid ? Icons.check_circle : Icons.error,
      color: _isMontantValid ? Colors.green : Colors.red,
      size: 20,
    );
  }

  String? _getMontantHelperText() {
    if (_montantController.text.trim().isEmpty) {
      return 'Entrez un multiple de 5 (minimum ${montantJ.toInt()} FCFA)';
    }

    if (_montantErrorMessage != null) {
      return _montantErrorMessage;
    }

    if (_isMontantValid) {
      try {
        if (useMonnaie) {
          double montantSaisi = double.parse(_montantController.text);
          int nombreJours = ((montantSaisi + monnaie!) / montantJ).floor();
          double monnaieRestante = (montantSaisi + monnaie!) % montantJ;
          return '✓ Valide - $nombreJours jour${nombreJours > 1 ? 's' : ''} + ${monnaieRestante.toInt()} FCFA de monnaie';
        } else {
          double montantSaisi = double.parse(_montantController.text);
          int nombreJours = (montantSaisi / montantJ).floor();
          double monnaieRestante = (montantSaisi + monnaie!) % montantJ;
          return '✓ Valide - $nombreJours jour${nombreJours > 1 ? 's' : ''} + ${monnaieRestante.toInt()} FCFA de monnaie';
        }
      } catch (e) {
        return null;
      }
    }

    return null;
  }

  Color _getMontantValidationColor() {
    if (_montantController.text.trim().isEmpty) {
      return Colors.grey.shade600;
    }

    return _isMontantValid ? Colors.green : Colors.red;
  }

  Future<void> checkTransactionWaveState() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;

      if (token == null) {
        print('Token manquant');
        return;
      }

      if (transactionCode == null) {
        print('Code de paiement manquant');
        return;
      }

      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/wave_pay_chek.php')),
        body: {
          'transaction_code': transactionCode!,
        },
        headers: {'Authorization': token},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        print('Wave transaction check: $responseData');

        if (responseData['payment_status'] == 'succeeded') {
          _showPaymentSuccess(responseData['payment_status']);
        } else if (responseData['payment_status'] == 'processing') {
          _showPaymentSuccess(responseData['payment_status']);
        } else {
          _showPaymentSuccess(responseData['payment_status']);
        }
      } else {
        print('Erreur Wave check : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la vérification Wave : $error');
    }
  }

  Future<void> checkTransactionState() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;

      if (token == null) {
        print('Token manquant');
        return;
      }
      if (transactionCode == null) {
        print('Code de paiement manquant');
        return;
      }

      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/CinetPayNotify.php')),
        body: {
          'cpm_trans_id': transactionCode!,
        },
        headers: {'Authorization': token},
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        print('CinetPay transaction check: $responseData');

        if (responseData['data']['status'] == 'ACCEPTED') {
          _showPaymentSuccess(responseData['data']['status']);
        } else if (responseData['data']['status'] == 'PENDING') {
          _showPaymentSuccess(responseData['data']['status']);
        } else {
          _showPaymentSuccess(responseData['data']['status']);
        }
      } else {
        print('Erreur CinetPay check : ${response.statusCode}');
      }
    } catch (error) {
      print('Erreur lors de la vérification CinetPay : $error');
    }
  }

  void _showPaymentSuccess(String status) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: _buildStatusCard(status),
        );
      },
    );
  }

  Future<void> _navigateConfirm() async {
    final token = Provider.of<AuthProvider>(context, listen: false).token;
    final provide = Provider.of<AuthProvider>(context, listen: false);
    final user = Provider.of<AuthProvider>(context, listen: false).user;
    String personnelId = user!['id_personnel'].toString();
    setState(() {
      isSubmitting = true;
    });
    if (token == null) {
      _showSafeSnackBar('Session expirée. Veuillez vous reconnecter.');
      if (mounted) {
        setState(() {
          isSubmitting = false;
        });
      }
      return;
    }

    if (!useMonnaie && (resteMonnaie + monnaie!) > montantJ) {
      showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.error, color: Colors.red, size: 48.0),
                SizedBox(height: 16.0),
                Text(
                  'Le total des monnaies dépasse le montant journalier. La monnaie doit être utilisée.',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          );
        },
      );
      setState(() {
        isSubmitting = false;
      });
      return;
    }
    final Map<String, dynamic> requestBody = {
      'transaction_id': transactionCode,
      'command_id': widget.commandeId,
      'client_id': widget.clientId,
      'personnel_id': personnelId,
      'monnaieExact': monnaieExact,
      'resteMonnaie': resteMonnaie,
      'monnaie': monnaie,
    };

    try {
      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/send_verify_sms.php')),
        body: json.encode(requestBody),
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json',
        },
      );
      print("response: ${(response.body)}");

      Map<String, dynamic>? responseData;
      try {
        responseData = jsonDecode(response.body);
      } catch (e) {
        print('Erreur parsing JSON: $e');
        setState(() {
          isSubmitting = false;
        });
        return;
      }

      if (response.statusCode == 200) {
        if (responseData != null && responseData['status'] == true) {
          setState(() {
            _isPageActive = false;
            isSubmitting = false;
          });
          Navigator.of(context, rootNavigator: true).pop();
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CodeVerificationPage(
                clientId: widget.clientId,
                commandeId: widget.commandeId,
                transactionId: transactionCode!,
                clientData: widget.clientData,
              ),
            ),
          );
        } else {
          _showSafeSnackBar(responseData?['message'] ?? 'Erreur inconnue');
          setState(() {
            _isPageActive = false;
            isSubmitting = false;
          });
          return;
        }
      } else {
        _showSafeSnackBar(
            responseData?['message'] ?? 'Erreur HTTP: ${response.statusCode}');
        setState(() {
          isSubmitting = false;
        });
        return;
      }
    } catch (error) {
      print("Erreur lors de l'appel API: $error");
      setState(() {
        isSubmitting = false;
      });
      _showSafeSnackBar('Erreur de connexion. Veuillez réessayer.',
          backgroundColor: Colors.red);
      //_showError("Erreur de connexion. Veuillez réessayer.");
    }
  }

  Widget _buildStatusCard(String status) {
    if (status == 'ACCEPTED' || status == 'succeeded') {
      // Appeler automatiquement _navigateConfirm après un court délai
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Future.delayed(const Duration(milliseconds: 1500), () {
          if (mounted && _isPageActive) {
            _navigateConfirm();
          }
        });
      });

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.green.shade100,
              borderRadius: BorderRadius.circular(40),
            ),
            child: Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 50,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Paiement Accepté',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Votre paiement a été vérifié avec succès!',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 20),
          const Center(
            child: CircularProgressIndicator(
              color: Colors.green,
              strokeWidth: 3.0,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Redirection en cours...',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
        ],
      );
    } else if (status == 'PENDING' || status == 'processing') {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.orange.shade100,
              borderRadius: BorderRadius.circular(40),
            ),
            child: Icon(
              Icons.schedule,
              color: Colors.orange,
              size: 50,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Paiement en Attente',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Votre paiement est en cours de vérification...',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // if (widget.paymentMethod == 'wave_ci') {
                //   checkTransactionWaveState();
                // } else {
                checkTransactionState();
                //}
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Vérifier à nouveau',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _clearPaymentCodeAndReload();
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Annuler la transaction',
                style: TextStyle(
                    fontSize: 16, color: Color.fromARGB(255, 0, 0, 0)),
              ),
            ),
          ),
        ],
      );
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.red.shade100,
              borderRadius: BorderRadius.circular(40),
            ),
            child: Icon(
              Icons.error,
              color: Colors.red,
              size: 50,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Paiement Échoué',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 10),
          const Text(
            'Votre paiement a été refusé. Veuillez réessayer.',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _clearPaymentCodeAndReload();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                padding: const EdgeInsets.symmetric(vertical: 15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              child: const Text(
                'Réessayer',
                style: TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
          ),
        ],
      );
    }
  }

  Future<void> _clearPaymentCodeAndReload() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('payment_code');
    await prefs.remove('commande_id');
    await prefs.remove('client_id');
    setState(() {
      transactionCode = null;
    });
    _reloadPage();
  }

  Future<void> fetchCommandeInfo() async {
    try {
      final provide = Provider.of<AuthProvider>(context, listen: false);
      final token = provide.token;

      if (token == null) {
        print('Token manquant pour fetchCommandeInfo');
        return;
      }

      final response = await http.get(
        Uri.parse(provide.getEndpoint(
            'products/getCommandeById.php?commandeId=${storageCommandeId ?? widget.commandeId}')),
        headers: {'Authorization': token, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);

        if (responseData.isNotEmpty) {
          final commandeData = responseData[0];

          if (mounted) {
            setState(() {
              code = commandeData['code_cmd']?.toString() ?? '';
              idCom = commandeData['id']?.toString();
              cle = commandeData['cle']?.toString();
              livret = commandeData['livret']?.toString();
              pack = commandeData['pack']?.toString();
              nomProduit = commandeData['code_cmd']?.toString();
              jour_paye = commandeData['paye']?.toString();
              journalier = commandeData['journalier']?.toString();
              montantJ =
                  double.tryParse(commandeData['journalier'] ?? '0') ?? 0;
              nbre_jours = commandeData['jour']?.toString();
              jour_reste = commandeData['reste']?.toString();
            });
          }
        }
      } else {
        print('Erreur fetchCommandeInfo : ${response.statusCode}');
      }
    } catch (error) {
      print(
          'Erreur lors de la récupération des informations de la commande : $error');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldMessenger(
      key: _scaffoldMessengerKey,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: Text(
            'Paiement ${_getPaymentMethodName()}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: _getPaymentMethodColor(),
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.white),
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      _getPaymentMethodColor(),
                      _getPaymentMethodColor().withOpacity(0.8),
                    ],
                  ),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(30),
                    bottomRight: Radius.circular(30),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(20, 0, 20, 40),
                  child: Column(
                    children: [
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(50),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 3,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.15),
                              blurRadius: 15,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(47),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Image.asset(
                              _getPaymentMethodImage(),
                              fit: BoxFit.contain,
                              width: double.infinity,
                              height: double.infinity,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      Text(
                        _getPaymentMethodName(),
                        style: const TextStyle(
                          fontSize: 28,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              blurRadius: 10,
                              color: Colors.black26,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'Paiement mobile sécurisé',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildClientInfoCard(),
                    const SizedBox(height: 24),
                    _buildPaymentForm(),
                    const SizedBox(height: 30),
                    _buildPaymentButton(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getPaymentMethodName() {
    switch (widget.paymentMethod) {
      case 'orange_money':
        return 'Orange Money';
      case 'mtn_momo':
        return 'MTN MoMo';
      case 'moov_money':
        return 'MOOV Money';
      case 'moov_ci':
        return 'MOOV CI';
      case 'wave_ci':
        return 'WAVE CI';
      default:
        return 'Inconnu';
    }
  }

  String _getChannels() {
    switch (widget.paymentMethod) {
      case 'orange_money':
        return 'MOBILE_MONEY';
      case 'mtn_momo':
        return 'MOBILE_MONEY';
      case 'moov_money':
        return 'MOBILE_MONEY';
      case 'wave_ci':
        return 'WALLET';
      default:
        return 'Inconnu';
    }
  }

  Color _getPaymentMethodColor() {
    switch (widget.paymentMethod) {
      case 'orange_money':
        return Colors.orange;
      case 'mtn_momo':
        return Colors.yellow.shade700;
      case 'moov_money':
        return Colors.blue;
      case 'wave_ci':
        return const Color.fromARGB(255, 44, 136, 240);
      default:
        return Colors.grey;
    }
  }

  Future<void> _processPayment() async {
    final provide = _getSafeAuthProvider();
    if (provide == null) {
      _showSafeSnackBar(
          'Erreur d\'authentification. Veuillez redémarrer l\'application.');
      return;
    }

    final token = provide.token;
    if (token == null) {
      _showSafeSnackBar('Session expirée. Veuillez vous reconnecter.');
      return;
    }

    final Map<String, dynamic> requestBody = {
      'amount': _montantController.text,
      'phone': _numeroController.text,
      'command_id': widget.commandeId,
      'client_id': widget.clientId,
      'channels': _getChannels(),
    };

    try {
      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/addTransaction.php')),
        body: json.encode(requestBody),
        headers: {'Authorization': token, 'Content-Type': 'application/json'},
      );

      final Map<String, dynamic> responseData = jsonDecode(response.body);
      print('Parsed response: $responseData');
      if (response.statusCode == 200) {
        if (response.body.trim().startsWith('{') ||
            response.body.trim().startsWith('[')) {
          print('Parsed response: $responseData');

          if (responseData['success'] == true) {
            final paymentUrl =
                responseData['payment_url']?.toString().replaceAll('\\', '') ??
                    '';
            saveData("payment_code", responseData['transaction_id']);
            saveData("commande_id", widget.commandeId);
            saveData("client_id", widget.clientId);
            if (paymentUrl.isNotEmpty) {
              await _launchPaymentUrl(paymentUrl);
            }
          } else {
            _showSafeSnackBar(
              responseData['message']?.toString() ?? 'Erreur de paiement',
            );
          }
        } else {
          print("Erreur serveur: ${response.body}");
          _showSafeSnackBar(
            "Erreur du serveur. Contactez l'administrateur.",
            duration: const Duration(seconds: 5),
          );
        }
      } else {
        print("HTTP Error: ${response.statusCode}");
        _showSafeSnackBar("Erreur HTTP: ${response.statusCode}");
      }
    } catch (error) {
      print("Erreur lors du paiement: $error");
      _showSafeSnackBar("Erreur lors du paiement. Veuillez réessayer.");
    }
  }

  Future<void> _launchPaymentUrl(String url) async {
    try {
      // Vérifier si c'est un paiement CinetPay et si on doit utiliser la WebView
      final isCinetPay = widget.paymentMethod.contains('cinetpay') ||
          url.contains('cinetpay') ||
          url.contains('checkout.cinetpay.com');

      if (isCinetPay) {
        final useWebView =
            await PaymentConfigService.shouldUseWebViewForCinetPay();
        if (useWebView) {
          await _launchCinetPayWebView(url);
        } else {
          // Utiliser le navigateur externe même pour CinetPay si configuré ainsi
          await launchUrl(
            Uri.parse(url),
            mode: LaunchMode.externalApplication,
          );
        }
      } else {
        // Pour les autres méthodes, utiliser le navigateur externe
        await launchUrl(
          Uri.parse(url),
          mode: LaunchMode.externalApplication,
        );
      }
    } catch (e) {
      print("Erreur: $e");
      await Clipboard.setData(ClipboardData(text: url));
      _showSafeSnackBar(
        "URL copiée dans le presse-papiers",
        backgroundColor: Colors.orange,
      );
    }
  }

  Future<void> _launchCinetPayWebView(String url) async {
    try {
      // Utiliser le service WebView directement
      await WebViewPaymentService.launchCinetPayWebView(
        context: context,
        paymentUrl: url,
        transactionId: transactionCode ?? 'unknown',
        commandId: widget.commandeId,
        clientId: widget.clientId,
        autoCloseOnCompletion: true, // Option pour fermer automatiquement
        onPaymentCompleted: () {
          _showSafeSnackBar(
            'Paiement réussi !',
            backgroundColor: Colors.green,
          );
          // Optionnel: naviguer vers une page de succès
          // Navigator.pushReplacementNamed(context, '/payment_success');
        },
        onPaymentFailed: (reason) {
          _showSafeSnackBar(
            'Paiement échoué: $reason',
            backgroundColor: Colors.red,
          );
        },
        onPaymentCancelled: () {
          _showSafeSnackBar(
            'Paiement annulé',
            backgroundColor: Colors.orange,
          );
        },
      );
    } catch (e) {
      print("Erreur WebView: $e");
      // Fallback vers le navigateur externe
      await launchUrl(
        Uri.parse(url),
        mode: LaunchMode.externalApplication,
      );
    }
  }

  Future<void> _processWavePayment() async {
    final provide = _getSafeAuthProvider();
    if (provide == null) {
      _showSafeSnackBar(
          'Erreur d\'authentification. Veuillez redémarrer l\'application.');
      return;
    }

    final token = provide.token;
    if (token == null) {
      _showSafeSnackBar('Session expirée. Veuillez vous reconnecter.');
      return;
    }

    final Map<String, dynamic> requestBody = {
      'amount': _montantController.text,
      'phone': _numeroController.text,
      'command_id': widget.commandeId,
      'client_id': widget.clientId,
    };

    try {
      final response = await http.post(
        Uri.parse(provide.getEndpoint('products/wave_init_pay.php')),
        body: json.encode(requestBody),
        headers: {'Authorization': token, 'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        String responseBody = response.body.trim();
        if (!responseBody.startsWith('{') && !responseBody.startsWith('[')) {
          _showSafeSnackBar('Erreur serveur: $responseBody');
          return;
        }

        try {
          final Map<String, dynamic> responseData = jsonDecode(responseBody);

          if (responseData.containsKey('wave_launch_url')) {
            final waveLaunchUrl =
                responseData['wave_launch_url']?.toString() ?? '';
            final paymentId = responseData['id']?.toString() ?? '';

            if (paymentId.isNotEmpty) {
              saveData("payment_code", paymentId);
              saveData("commande_id", widget.commandeId);
              saveData("client_id", widget.clientId);
            }

            if (waveLaunchUrl.isNotEmpty) {
              await _launchPaymentUrl(waveLaunchUrl);
            }
          } else {
            _showSafeSnackBar('Erreur: URL de paiement Wave non trouvée');
          }
        } catch (e) {
          print('Erreur parsing JSON: $e');
          _showSafeSnackBar('Erreur de format de réponse du serveur');
        }
      } else if (response.statusCode == 401) {
        _showSafeSnackBar(
            'Erreur d\'authentification Wave API. Vérifiez la clé API.');
      } else {
        try {
          final errorData = jsonDecode(response.body);
          _showSafeSnackBar(
              'Erreur Wave: ${errorData['message'] ?? 'Erreur inconnue'}');
        } catch (e) {
          _showSafeSnackBar('Erreur HTTP: ${response.statusCode}');
        }
      }
    } catch (error) {
      print("Erreur lors du paiement Wave: $error");
      _showSafeSnackBar("Erreur lors du paiement Wave. Veuillez réessayer.");
    }
  }

  Widget _buildClientInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      _getPaymentMethodColor().withOpacity(0.1),
                      _getPaymentMethodColor().withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(
                  Icons.person,
                  color: _getPaymentMethodColor(),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Informations client',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade800,
                      ),
                    ),
                    Text(
                      'Détails de la transaction',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Divider(color: Colors.grey.shade200),
          const SizedBox(height: 16),
          _buildInfoRow(Icons.person_outline, 'Client',
              '${widget.clientData['nom_client'] ?? widget.clientData['nom'] ?? ''} ${widget.clientData['prenom_client'] ?? widget.clientData['prenom'] ?? ''}'),
          const SizedBox(height: 12),
          _buildInfoRow(Icons.qr_code, 'Code client',
              '${widget.clientData['code_client'] ?? ''}'),
          const SizedBox(height: 12),
          _buildInfoRow(Icons.receipt_long, 'Commande', code),
          const SizedBox(height: 12),
          _buildInfoRow(Icons.menu_book, 'Carnet', livret ?? ''),
          const SizedBox(height: 12),
          _buildInfoRow(Icons.inventory, 'Pack', pack ?? ''),
          const SizedBox(height: 12),
          _buildInfoRow(Icons.calendar_month, 'Journalier', journalier ?? ''),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18,
          color: Colors.grey.shade600,
        ),
        const SizedBox(width: 12),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey.shade600,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentForm() {
    final userDetail = Provider.of<AuthProvider>(context, listen: false).user;
    final userPhone1 = userDetail?['telephone_pers'];
    final userPhone2 = userDetail?['telephone_service_pers'];

    final clientPhone = widget.clientData['telephone'] ??
        widget.clientData['telephone_client'] ??
        '';

    bool isSelfPayment1 = userPhone1 == clientPhone;
    bool isSelfPayment2 = userPhone2 == clientPhone;

    // Afficher un message d'erreur via SnackBar au lieu d'un dialog bloquant
    if (isSelfPayment1 || isSelfPayment2) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showSafeSnackBar(
          'Erreur: Vous ne pouvez pas effectuer un versement vers votre propre numéro.',
          duration: const Duration(seconds: 4),
        );
      });
      _numeroController.text = clientPhone;
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informations de paiement',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 16),

          // Section Monnaie avec logique obligatoire
          _buildMonnaieSection(),
          const SizedBox(height: 20),
          TextFormField(
            controller: _montantController,
            keyboardType: TextInputType.number,
            onChanged: (value) {
              _validateMontant(value);
            },
            decoration: InputDecoration(
              labelText: 'Montant à verser',
              hintText: journalier != null
                  ? 'Ex: $journalier, ${(double.parse(journalier!) * 2).toInt()}, ${(double.parse(journalier!) * 3).toInt()}...'
                  : 'Entrez un montant',
              prefixIcon:
                  Icon(Icons.attach_money, color: _getPaymentMethodColor()),
              suffixIcon: _getMontantValidationIcon(),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: _getPaymentMethodColor()),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(color: Colors.red),
              ),
              helperText: _getMontantHelperText(),
              helperStyle: TextStyle(
                color: _getMontantValidationColor(),
                fontSize: 12,
              ),
            ),
          ),
          const SizedBox(height: 20),
          TextFormField(
            controller: _numeroController,
            enabled: !(isSelfPayment1 || isSelfPayment2),
            keyboardType: TextInputType.phone,
            onChanged: (value) {
              if (value == userPhone1 || value == userPhone2) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  _showSafeSnackBar(
                    'Erreur: Vous ne pouvez pas entrer votre propre numéro.',
                    duration: const Duration(seconds: 3),
                  );
                });
                _numeroController.text = '';
              }
            },
            decoration: InputDecoration(
              labelText: 'Numéro de téléphone',
              prefixIcon: Icon(Icons.phone, color: _getPaymentMethodColor()),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: _getPaymentMethodColor()),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMonnaieSection() {
    double monnaieDisponible = monnaie ?? 0;
    bool monnaieObligatoire = false;

    // Vérifier si la monnaie est obligatoire selon les nouvelles règles
    // Cas 1 : Si monnaie >= montant journalier, utilisation obligatoire
    if (monnaieDisponible >= montantJ) {
      monnaieObligatoire = true;
      // Forcer automatiquement l'utilisation de la monnaie
      if (!useMonnaie) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          setState(() {
            useMonnaie = true;
            recalculerMontants(setState);
          });
        });
      }
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: monnaieObligatoire ? Colors.orange.shade50 : Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: monnaieObligatoire
              ? Colors.orange.shade300
              : Colors.blue.shade300,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: monnaieObligatoire
                    ? Colors.orange.shade600
                    : Colors.blue.shade600,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Monnaie disponible',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: monnaieObligatoire
                      ? Colors.orange.shade700
                      : Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          Text(
            '${monnaieDisponible.toInt()} FCFA',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: monnaieObligatoire
                  ? Colors.orange.shade800
                  : Colors.blue.shade800,
            ),
          ),

          const SizedBox(height: 12),

          // Message d'information sur les règles
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.grey.shade600, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    monnaieObligatoire
                        ? 'Votre monnaie (${monnaieDisponible.toInt()} FCFA) est ≥ au montant journalier. Son utilisation est obligatoire.'
                        : 'Vous pouvez choisir d\'utiliser votre monnaie (${monnaieDisponible.toInt()} FCFA) ou non.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),

          // Switch pour utiliser la monnaie (toujours affiché)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                monnaieObligatoire
                    ? 'Utilisation de la monnaie'
                    : 'Utiliser la monnaie',
                style: TextStyle(
                  fontSize: 14,
                  color: monnaieObligatoire
                      ? Colors.orange.shade700
                      : Colors.blue.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Switch(
                value: useMonnaie,
                onChanged: (bool value) {
                  setState(() {
                    useMonnaie = value;
                    recalculerMontants(setState);
                    // Revalider le montant après changement
                    _validateMontant(_montantController.text);
                  });
                },
                activeColor: monnaieObligatoire
                    ? Colors.orange.shade600
                    : Colors.blue.shade600,
              ),
            ],
          ),

          // Affichage des calculs si monnaie utilisée
          if (useMonnaie || monnaieObligatoire) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Column(
                children: [
                  if (quotient > 0) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Jours payés:',
                            style: TextStyle(
                                fontSize: 12, color: Colors.grey.shade600)),
                        Text('$quotient jour${quotient > 1 ? 's' : ''}',
                            style: const TextStyle(
                                fontSize: 12, fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ],
                  if (monnaieExact > 0) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Monnaie utilisée:',
                            style: TextStyle(
                                fontSize: 12, color: Colors.grey.shade600)),
                        Text('$monnaieExact FCFA',
                            style: const TextStyle(
                                fontSize: 12, fontWeight: FontWeight.bold)),
                      ],
                    ),
                  ],
                  if (resteMonnaie > 0) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('Nouvelle monnaie:',
                            style: TextStyle(
                                fontSize: 12, color: Colors.grey.shade600)),
                        Text('${resteMonnaie.toInt()} FCFA',
                            style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.green)),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getPaymentMethodColor(),
            _getPaymentMethodColor().withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: _getPaymentMethodColor().withOpacity(0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () {
          // Validation des champs
          if (_montantController.text.trim().isEmpty) {
            _showSafeSnackBar('Veuillez entrer un montant');
            return;
          }

          if (!_isMontantValid) {
            _showSafeSnackBar(_montantErrorMessage ?? 'Montant invalide');
            return;
          }

          if (_numeroController.text.trim().isEmpty) {
            _showSafeSnackBar('Veuillez entrer un numéro de téléphone');
            return;
          }

          // Vérification de sécurité supplémentaire
          final provide = _getSafeAuthProvider();
          if (provide != null) {
            final userDetail = provide.user;
            final userPhone1 = userDetail?['telephone_pers'];
            final userPhone2 = userDetail?['telephone_service_pers'];

            if (_numeroController.text.trim() == userPhone1 ||
                _numeroController.text.trim() == userPhone2) {
              _showSafeSnackBar(
                'Erreur: Vous ne pouvez pas effectuer un paiement vers votre propre numéro',
                duration: const Duration(seconds: 4),
              );
              return;
            }
          }

          print('Tentative de paiement avec méthode: ${widget.paymentMethod}');
          print('Montant: ${_montantController.text}');
          print('Numéro: ${_numeroController.text}');

          if (widget.paymentMethod == 'wave_ci') {
            _processWavePayment();
          } else {
            _processPayment();
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              _getPaymentMethodImage(),
              width: 24,
              height: 24,
            ),
            const SizedBox(width: 12),
            Text(
              'Payer via ${_getPaymentMethodName()}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getPaymentMethodImage() {
    switch (widget.paymentMethod) {
      case 'orange_money':
        return 'assets/orange_money.png';
      case 'mtn_momo':
        return 'assets/mtn_momo.png';
      case 'moov_money':
        return 'assets/moov_money.png';
      case 'wave_ci':
        return 'assets/wave_ci.png';
      default:
        return 'assets/logo.png';
    }
  }
}
