import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:callitris/services/app_logger.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:async';

/// Widget WebView pour les paiements CinetPay intégrés dans l'application
class PaymentWebView extends StatefulWidget {
  final String paymentUrl;
  final String transactionId;
  final String? commandId;
  final String? clientId;
  final VoidCallback? onPaymentCompleted;
  final Function(String)? onPaymentFailed;
  final VoidCallback? onPaymentCancelled;
  final bool autoCloseOnCompletion;

  const PaymentWebView({
    Key? key,
    required this.paymentUrl,
    required this.transactionId,
    this.commandId,
    this.clientId,
    this.onPaymentCompleted,
    this.onPaymentFailed,
    this.onPaymentCancelled,
    this.autoCloseOnCompletion = true,
  }) : super(key: key);

  @override
  State<PaymentWebView> createState() => _PaymentWebViewState();
}

class _PaymentWebViewState extends State<PaymentWebView> {
  late final WebViewController _controller;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  bool _paymentCompleted = false;
  Timer? _timeoutTimer;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
    _startTimeoutTimer();
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            if (progress == 100) {
              setState(() {
                _isLoading = false;
              });
            }
          },
          onPageStarted: (String url) {
            AppLogger.info(
                'WebView navigation started: $url', 'PaymentWebView');
            _handleUrlNavigation(url);
          },
          onPageFinished: (String url) {
            AppLogger.info(
                'WebView navigation finished: $url', 'PaymentWebView');
            setState(() {
              _isLoading = false;
            });
            _handleUrlNavigation(url);
          },
          onWebResourceError: (WebResourceError error) {
            AppLogger.error(
                'WebView error: ${error.description}', 'PaymentWebView');
            setState(() {
              _hasError = true;
              _errorMessage = error.description;
              _isLoading = false;
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            AppLogger.info(
                'WebView navigation request: ${request.url}', 'PaymentWebView');

            // Intercepter UNIQUEMENT les URLs de statut final CinetPay
            if (request.url
                .contains('checkout.cinetpay.com/payment/status/failed')) {
              AppLogger.warning(
                  'URL d\'échec CinetPay interceptée dans navigation request',
                  'PaymentWebView');
              _handleCinetPayFailure(request.url);
              return NavigationDecision.prevent;
            }

            // Intercepter UNIQUEMENT les URLs de succès final CinetPay
            if (request.url
                    .contains('checkout.cinetpay.com/payment/status/success') ||
                request.url.contains(
                    'checkout.cinetpay.com/payment/status/completed')) {
              AppLogger.info(
                  'URL de succès CinetPay interceptée dans navigation request',
                  'PaymentWebView');
              _handlePaymentSuccess({});
              return NavigationDecision.prevent;
            }

            // Intercepter les deep links
            if (request.url.startsWith('callitris://')) {
              _handleDeepLink(request.url);
              return NavigationDecision.prevent;
            }

            // Intercepter les URLs de callback
            if (request.url.contains('deep_links_config/backend/')) {
              _handleCallbackUrl(request.url);
              return NavigationDecision.prevent;
            }

            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.paymentUrl));
  }

  void _startTimeoutTimer() {
    // Timer de 10 minutes pour éviter que l'utilisateur reste bloqué
    _timeoutTimer = Timer(const Duration(minutes: 10), () {
      if (!_paymentCompleted && mounted) {
        _handlePaymentTimeout();
      }
    });
  }

  void _handleUrlNavigation(String url) {
    AppLogger.info('Navigation URL détectée: $url', 'PaymentWebView');

    // Détecter UNIQUEMENT les URLs de statut final CinetPay
    if (url == 'https://checkout.cinetpay.com/payment/status/failed') {
      AppLogger.warning(
          'URL d\'échec CinetPay détectée - Redirection vers navigateur',
          'PaymentWebView');
      _handleCinetPayFailure(url);
      return;
    }

    if (url == 'https://checkout.cinetpay.com/payment/status/success' ||
        url == 'checkout.cinetpay.com/payment/status/completed') {
      AppLogger.info('URL de succès CinetPay détectée', 'PaymentWebView');
      _handlePaymentSuccess({});
      return;
    }

    // Vérifier d'autres patterns de succès/échec génériques
    if (url == 'https://checkout.cinetpay.com/payment/status/success' || url == 'https://checkout.cinetpay.com/payment/status/completed') {
      _handlePaymentResult(url);
    } else if (url == 'https://checkout.cinetpay.com/payment/status/failed') {
      _handlePaymentResult(url);
    }
  }

  void _handleDeepLink(String deepLinkUrl) {
    AppLogger.info('Deep link intercepté: $deepLinkUrl', 'PaymentWebView');

    final uri = Uri.parse(deepLinkUrl);
    final path = uri.path;
    final params = uri.queryParameters;

    if (path.contains('success')) {
      _handlePaymentSuccess(params);
    } else if (path.contains('failure')) {
      _handlePaymentFailure(params);
    } else if (path.contains('pending')) {
      _handlePaymentPending(params);
    }
  }

  void _handleCallbackUrl(String callbackUrl) {
    AppLogger.info(
        'URL de callback interceptée: $callbackUrl', 'PaymentWebView');

    final uri = Uri.parse(callbackUrl);
    final params = uri.queryParameters;

    // Analyser les paramètres pour déterminer le statut
    final status = params['status'] ?? '';

    if (status == 'success') {
      _handlePaymentSuccess(params);
    } else if (status == 'failure') {
      _handlePaymentFailure(params);
    } else {
      // Attendre un peu plus pour voir si le statut change
      Future.delayed(const Duration(seconds: 2), () {
        if (!_paymentCompleted) {
          _handlePaymentPending(params);
        }
      });
    }
  }

  void _handlePaymentResult(String url) {
    if (url.contains('success') || url.contains('completed')) {
      _handlePaymentSuccess({});
    } else if (url.contains('failed') || url.contains('cancel')) {
      _handlePaymentFailure({});
    }
  }

  void _handlePaymentSuccess(Map<String, String> params) {
    if (_paymentCompleted) return;

    setState(() {
      _paymentCompleted = true;
    });

    _timeoutTimer?.cancel();

    AppLogger.info('Paiement réussi dans WebView', 'PaymentWebView');

    if (widget.onPaymentCompleted != null) {
      widget.onPaymentCompleted!();
    }

    if (widget.autoCloseOnCompletion) {
      _closeWebView();
    } else {
      _showCompletionDialog('Paiement réussi !', Colors.green);
    }
  }

  void _handlePaymentFailure(Map<String, String> params) {
    if (_paymentCompleted) return;

    setState(() {
      _paymentCompleted = true;
    });

    _timeoutTimer?.cancel();

    final reason = params['reason'] ?? 'Paiement échoué';
    AppLogger.warning(
        'Paiement échoué dans WebView: $reason', 'PaymentWebView');

    if (widget.onPaymentFailed != null) {
      widget.onPaymentFailed!(reason);
    }

    if (widget.autoCloseOnCompletion) {
      _closeWebView();
    } else {
      _showCompletionDialog('Paiement échoué: $reason', Colors.red);
    }
  }

  void _handleCinetPayFailure(String failureUrl) {
    if (_paymentCompleted) return;

    setState(() {
      _paymentCompleted = true;
    });

    _timeoutTimer?.cancel();

    AppLogger.warning(
        'Paiement ',
        'PaymentWebView');

    // Fermer la WebView
    _closeWebView();

    // Ouvrir dans le navigateur externe après un court délai
    Future.delayed(const Duration(milliseconds: 500), () async {
      try {
        final Uri uri = Uri.parse(failureUrl);
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        AppLogger.info('URL d\'échec ouverte dans le navigateur: $failureUrl',
            'PaymentWebView');
      } catch (e) {
        AppLogger.error(
            'Erreur lors de l\'ouverture du navigateur: $e', 'PaymentWebView');
        // Notifier l'échec si impossible d'ouvrir le navigateur
        if (widget.onPaymentFailed != null) {
          widget.onPaymentFailed!(
              'Échec du paiement - Impossible d\'ouvrir le navigateur');
        }
      }
    });

    // Notifier l'échec
    if (widget.onPaymentFailed != null) {
      widget.onPaymentFailed!('Paiement échoué - Redirection vers navigateur');
    }
  }

  void _handlePaymentPending(Map<String, String> params) {
    AppLogger.info('Paiement en attente dans WebView', 'PaymentWebView');
    // Ne pas fermer automatiquement pour les paiements en attente
  }

  void _handlePaymentTimeout() {
    AppLogger.warning('Timeout du paiement dans WebView', 'PaymentWebView');

    if (widget.onPaymentFailed != null) {
      widget.onPaymentFailed!('Timeout du paiement');
    }

    _showCompletionDialog('Le paiement a pris trop de temps', Colors.orange);
  }

  void _showCompletionDialog(String message, Color color) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Icon(
            _paymentCompleted ? Icons.check_circle : Icons.error,
            color: color,
            size: 48,
          ),
          content: Text(
            message,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _closeWebView();
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _closeWebView() {
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  void _cancelPayment() {
    _timeoutTimer?.cancel();

    if (widget.onPaymentCancelled != null) {
      widget.onPaymentCancelled!();
    }

    AppLogger.info('Paiement annulé par l\'utilisateur', 'PaymentWebView');
    _closeWebView();
  }

  void _refreshWebView() {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });
    _controller.reload();
  }

  @override
  void dispose() {
    _timeoutTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Paiement CinetPay'),
        backgroundColor: const Color.fromARGB(255, 249, 221, 175),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: _cancelPayment,
        ),
        actions: [
          if (_hasError)
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _refreshWebView,
              tooltip: 'Actualiser',
            ),
          IconButton(
            icon: const Icon(Icons.cancel),
            onPressed: _cancelPayment,
            tooltip: 'Annuler le paiement',
          ),
        ],
      ),
      body: Stack(
        children: [
          if (_hasError)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Erreur de chargement',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _errorMessage,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: _refreshWebView,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Réessayer'),
                  ),
                  const SizedBox(height: 8),
                  TextButton(
                    onPressed: _cancelPayment,
                    child: const Text('Annuler'),
                  ),
                ],
              ),
            )
          else
            WebViewWidget(controller: _controller),
          if (_isLoading)
            Container(
              color: Colors.white.withOpacity(0.8),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Chargement du paiement...',
                      style: TextStyle(fontSize: 16),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          border: Border(
            top: BorderSide(color: Colors.grey[300]!),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                'Transaction: ${widget.transactionId}',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ),
            TextButton.icon(
              onPressed: _cancelPayment,
              icon: const Icon(Icons.close, size: 16),
              label: const Text('Annuler'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
